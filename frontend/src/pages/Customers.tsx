import React, { useState, useEffect, useCallback } from "react";

import { Layout } from "@/components/layout/Layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge, badgeVariants } from "@/components/ui/badge";
import { Plus, Search, Filter, Eye, Edit, Trash2, CreditCard, Loader2, DollarSign, User, Image, AlertCircle, Phone, Calendar, Building2, X } from "lucide-react";
import { CustomerForm } from "@/components/customers/CustomerForm";
import { CustomerCreateForm } from "@/components/customers/CustomerCreateForm";
import { CustomerDetail } from "@/components/customers/CustomerDetail";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomerCombobox } from "@/components/customers/CustomerCombobox";
import { getCustomerList, deleteCustomer } from "@/services/customerService";
import { Customer as CustomerType } from "@/models/customer";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import { CardAttachmentIndicator } from '@/components/ui/card-attachment';
import apiClient from "@/lib/apiClient";
import { getCustomerStats } from "@/services/statsService";
import { formatDateTimeVN } from "@/utils/date-format";

// Define the type for view mode
type ViewMode = "list" | "detail" | "form";

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [customers, setCustomers] = useState<CustomerType[]>([]);
  const [filteredCustomers, setFilteredCustomers] = useState<CustomerType[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>("list");
  const [selectedCustomerId, setSelectedCustomerId] = useState<string | null>(null);
  const [loading, setLoading] = useState(true);
  const [reload, setReload] = useState(0);
  const [showCreateCustomerModal, setShowCreateCustomerModal] = useState(false);
  const [showEditCustomerModal, setShowEditCustomerModal] = useState(false);
  const [showFilterModal, setShowFilterModal] = useState(false);

  // State cho thống kê
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalOutstandingFee: 0
  });

  // Thêm state cho bộ lọc khách hàng
  const [customerFilters, setCustomerFilters] = useState({
    hasOutstandingFee: false // Lọc khách hàng đang nợ phí
  });
  const [showCustomerFilterModal, setShowCustomerFilterModal] = useState(false);
  const [editingCustomerId, setEditingCustomerId] = useState<string | null>(null);

  // Add page title and check localStorage
  useEffect(() => {
    document.title = "Quản lý khách hàng | Tín Phát Credit";

    // Kiểm tra xem có yêu cầu mở form tạo mới từ OrderCreateModal không
    const openCustomerForm = localStorage.getItem('openCustomerForm');
    if (openCustomerForm === 'true') {
      // Xóa giá trị đã sử dụng
      localStorage.removeItem('openCustomerForm');
      // Mở form tạo mới
      setSelectedCustomerId(null);
      setViewMode("form");
    }

    // Lấy thống kê khách hàng
    fetchCustomerStats();
  }, []);

  // Hàm lấy thống kê khách hàng
  const fetchCustomerStats = async () => {
    try {
      const response = await getCustomerStats();
      if (response.success) {
        setStats(response.data);
      }
    } catch (error) {
      console.error("Error fetching customer stats:", error);
    }
  };

  // Thêm state cho phân trang
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);

  // Fetch customers data with pagination
  useEffect(() => {
    setLoading(true);
    getCustomerList(
      currentPage,
      itemsPerPage,
      searchTerm,
      customerFilters.hasOutstandingFee
    )
      .then(response => {
        if (response.success) {
          setCustomers(response.data || []);
          setFilteredCustomers(response.data || []);

          // Cập nhật thông tin phân trang
          if (response.pagination) {
            setTotalPages(response.pagination.totalPages);
            setTotalItems(response.pagination.total);
          }

          // Lấy thống kê khách hàng
          fetchCustomerStats();
        } else {
          toast.error(response.message || "Không thể tải danh sách khách hàng");
        }
      })
      .catch(error => {
        console.error("Error fetching customers:", error);
        toast.error("Lỗi khi tải danh sách khách hàng");
      })
      .finally(() => {
        setLoading(false);
      });
  }, [reload, currentPage, itemsPerPage, searchTerm, customerFilters.hasOutstandingFee]);



  // Filter customers based on search term and filters
  useEffect(() => {
    // Bắt đầu với toàn bộ danh sách khách hàng
    let filtered = [...customers];

    // Áp dụng bộ lọc khách hàng đang nợ phí
    if (customerFilters.hasOutstandingFee) {
      filtered = filtered.filter(customer => {
        // Lấy giá trị nợ phí
        const fee = customer.outstanding_fee !== undefined
          ? customer.outstanding_fee
          : (customer.outstandingFee || 0);

        // Chuyển đổi thành số nếu là chuỗi
        const numFee = typeof fee === 'string' ? parseFloat(fee) : fee;

        // Chỉ giữ lại khách hàng có nợ phí > 0
        return numFee > 0;
      });
    }

    // Áp dụng tìm kiếm
    if (searchTerm) {
      const lowercaseSearch = searchTerm.toLowerCase();
      filtered = filtered.filter(
        customer =>
          customer.name.toLowerCase().includes(lowercaseSearch) ||
          (customer.phone && customer.phone.includes(searchTerm)) ||
          customer.id.toString().includes(lowercaseSearch) ||
          (customer.cards && customer.cards.some(card =>
            (card.number || card.card_number || '').toLowerCase().includes(lowercaseSearch)
          ))
      );
    }

    setFilteredCustomers(filtered);
  }, [searchTerm, customers, customerFilters]);



  // Hiển thị hạn mức định dạng thành tiền VND
  const formatCurrency = (amount: number | string | undefined) => {
    if (amount === undefined || amount === null) return "0";
    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
    return new Intl.NumberFormat('vi-VN', { maximumFractionDigits: 0 }).format(numAmount);
  };

  // Tính tổng nợ phí của tất cả khách hàng (chỉ tính số dương)
  const getTotalOutstandingFee = () => {
    // Đảm bảo rằng chúng ta đang sử dụng toàn bộ danh sách khách hàng, không chỉ những khách hàng đã được lọc
    return customers.reduce((total, customer) => {
      // Kiểm tra nếu có outstanding_fee hoặc outstandingFee
      const fee = customer.outstanding_fee !== undefined
        ? customer.outstanding_fee
        : (customer.outstandingFee || 0);

      // Chuyển đổi thành số nếu là chuỗi
      const numFee = typeof fee === 'string' ? parseFloat(fee) : fee;

      // Chỉ cộng dồn các giá trị dương (khách hàng đang nợ)
      return total + (isNaN(numFee) ? 0 : (numFee > 0 ? numFee : 0));
    }, 0);
  };

  // Đếm số thẻ của khách hàng
  const countCustomerCards = (customer: CustomerType) => {
    try {
      // Kiểm tra nếu có card_count từ API
      if ((customer as any).card_count !== undefined) {
        return Number((customer as any).card_count);
      }

      // Backup: Kiểm tra nếu customer.cards là mảng
      if (customer.cards && Array.isArray(customer.cards)) {
        return customer.cards.length;
      }

      return 0;
    } catch (error) {
      console.error("Error counting cards:", error);
      return 0;
    }
  };

  // Utility function for calculating days until a date
  const calculateDaysUntil = (date: number | null): { days: number; isNear: boolean } => {
    if (!date) return { days: 0, isNear: false };

    const today = new Date();
    const currentMonth = today.getMonth();
    const currentYear = today.getFullYear();

    // Create date object for payment date in current month
    const targetDate = new Date(currentYear, currentMonth, date);

    // If the date has already passed this month, look at next month
    if (targetDate < today) {
      targetDate.setMonth(targetDate.getMonth() + 1);
    }

    // Calculate days until the target date
    const diffTime = targetDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Consider it near if within 20 days
    const isNear = diffDays <= 20;

    return { days: diffDays, isNear };
  };



  // Handle viewing customer details
  const handleViewDetail = (customerId: string) => {
    setSelectedCustomerId(customerId);
    setViewMode("detail");
  };

  // Handle editing a customer
  const handleEditCustomer = (customerId: string) => {
    setEditingCustomerId(customerId);
    setShowEditCustomerModal(true);
  };

  // Handle editing a card
  const handleEditCard = (card: any) => {
    setInitialCardData(card);
    setSelectedCustomerId(card.customer_id.toString());
    setViewMode("form");

    // Establecer la pestaña de tarjetas como activa en el formulario
    localStorage.setItem("customerFormActiveTab", "card");
  };

  // Xử lý thành công khi chỉnh sửa khách hàng
  const handleEditCustomerSuccess = () => {
    toast.success('Đã cập nhật thông tin khách hàng thành công');
    setReload(prev => prev + 1);
    setShowEditCustomerModal(false);
    setEditingCustomerId(null);
  };

  // Xử lý thành công khi chỉnh sửa thẻ
  const handleEditCardSuccess = () => {
    toast.success('Đã cập nhật thông tin thẻ thành công');
    setReload(prev => prev + 1);
    setShowEditCardModal(false);
    setEditingCard(null);
  };

  // Render modal chỉnh sửa khách hàng
  const renderEditCustomerModal = () => {
    return (
      <Dialog open={showEditCustomerModal} onOpenChange={setShowEditCustomerModal}>
        <DialogContent className="max-w-[800px] max-h-[90vh] overflow-y-auto" aria-labelledby="edit-customer-title" aria-describedby="edit-customer-description">
          <DialogHeader>
            <DialogTitle id="edit-customer-title">Chỉnh sửa khách hàng</DialogTitle>
            <DialogDescription id="edit-customer-description">Cập nhật thông tin khách hàng đã chọn</DialogDescription>
          </DialogHeader>
          <CustomerForm
            customerId={editingCustomerId}
            onCancel={() => setShowEditCustomerModal(false)}
            onSuccess={handleEditCustomerSuccess}
          />
        </DialogContent>
      </Dialog>
    );
  };



  // Handle showing modal to create a new customer
  const handleShowCreateCustomerModal = () => {
    setShowCreateCustomerModal(true);
  };

  // Handle success from customer form (create/edit)
  const handleCustomerFormSuccess = () => {
    setShowCreateCustomerModal(false);
    setViewMode("list");
    // Refresh data
    setReload(prev => prev + 1);
  };

  // Handle returning to the list view
  const handleBackToList = () => {
    setViewMode("list");
    setSelectedCustomerId(null);
    // Refresh data when returning to list
    setReload(prev => prev + 1);
  };

  // Handle deleting a customer
  const handleDeleteCustomer = (customerId: string) => {
    if (window.confirm(`Bạn có chắc chắn muốn xóa khách hàng này không?`)) {
      deleteCustomer(Number(customerId))
        .then(response => {
          if (response.success) {
            toast.success("Đã xóa khách hàng thành công");
            // Refresh the list
            setReload(prev => prev + 1);
          } else {
            toast.error(response.message || "Không thể xóa khách hàng");
          }
        })
        .catch(error => {
          console.error("Error deleting customer:", error);
          toast.error("Lỗi khi xóa khách hàng");
        });
    }
  };

  // Thêm hàm kiểm tra thẻ có liên kết với đơn hàng không
  const checkCardHasOrders = async (cardId: number) => {
    try {
      const response = await apiClient.get(`/api/cards/${cardId}/orders`);
      console.log('Card orders check response:', response.data);
      if (response.data.success && response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
        return {
          hasOrders: true,
          orderCount: response.data.data.length
        };
      }
      return {
        hasOrders: false,
        orderCount: 0
      };
    } catch (error) {
      console.error('Error checking card orders:', error);
      return {
        hasOrders: false,
        orderCount: 0
      };
    }
  };

  // Thêm hàm xử lý xóa thẻ
  const handleDeleteCard = async (cardId: number, cardNumber: string) => {
    try {
      // Kiểm tra xem thẻ có liên kết với đơn hàng không
      const { hasOrders, orderCount } = await checkCardHasOrders(cardId);

      if (hasOrders) {
        toast.error(`Thẻ ${cardNumber} có liên kết với ${orderCount} đơn hàng nên không thể xóa. Vui lòng liên hệ admin để xử lý.`);
        return;
      }

      // Nếu không có liên kết, hiển thị xác nhận xóa
      if (window.confirm(`Bạn có chắc chắn muốn xóa thẻ ${cardNumber} không?`)) {
        const response = await apiClient.delete(`/api/cards/${cardId}`);

        if (response.data.success) {
          toast.success('Đã xóa thẻ thành công');
          // Refresh danh sách thẻ
          setReload(prev => prev + 1);
        } else {
          toast.error(response.data.message || 'Không thể xóa thẻ');
        }
      }
    } catch (error: any) {
      console.error('Error deleting card:', error);
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('Đã xảy ra lỗi khi xóa thẻ');
      }
    }
  };


  // Add function to check if a card has "Đáo" orders in the current month
  const hasRecentDaoOrder = async (card: any) => {
    if (!card) return false;

    // Chỉ áp dụng với thẻ có is_holding_card=1
    if (!isHoldingCard(card)) return false;

    const cardId = card.id;
    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Kiểm tra nếu card có orders
    if (card.orders && Array.isArray(card.orders)) {
      console.log(`[DEBUG] Kiểm tra Đáo trực tiếp từ card.orders cho thẻ ${card.id}:`, card.orders);
      // Kiểm tra trực tiếp từ card.orders
      return card.orders.some((order: any) => {
        const orderDate = new Date(order.created_at);
        const isDaoOrder = order.order_type === "Đáo";
        const isCurrentMonth = orderDate >= firstDayOfMonth && orderDate <= today;
        console.log(`[DEBUG] Order ${order.id}: type=${order.order_type}, date=${orderDate}, isDaoOrder=${isDaoOrder}, isCurrentMonth=${isCurrentMonth}`);
        return isDaoOrder && isCurrentMonth;
      });
    }

    try {
      // Nếu không có orders trong card, gọi API để lấy danh sách đơn hàng của thẻ
      console.log(`[DEBUG] Gọi API để lấy đơn hàng cho thẻ ${cardId}`);
      const response = await apiClient.get(`/api/cards/${cardId}/orders`);

      if (response.data.success && response.data.data && Array.isArray(response.data.data)) {
        console.log(`[DEBUG] Kết quả API đơn hàng cho thẻ ${cardId}:`, response.data.data);

        // Kiểm tra có đơn hàng Đáo trong tháng hiện tại không
        const hasDaoOrder = response.data.data.some((order: any) => {
          if (!order.order_type || !order.created_at) {
            console.log(`[DEBUG] Order không có order_type hoặc created_at:`, order);
            return false;
          }

          const orderDate = new Date(order.created_at);
          const isDaoOrder = order.order_type.includes("Đáo");
          const isCurrentMonth = orderDate >= firstDayOfMonth && orderDate <= today;

          console.log(`[DEBUG] Order ${order.id}: type=${order.order_type}, date=${orderDate.toISOString()}, isDaoOrder=${isDaoOrder}, isCurrentMonth=${isCurrentMonth}`);

          return isDaoOrder && isCurrentMonth;
        });

        console.log(`[DEBUG] Thẻ ${cardId} có đơn hàng Đáo trong tháng: ${hasDaoOrder}`);
        return hasDaoOrder;
      } else {
        console.log(`[DEBUG] Không có dữ liệu đơn hàng cho thẻ ${cardId}:`, response.data);
      }

      return false;
    } catch (error) {
      console.error(`[ERROR] Lỗi khi kiểm tra đơn hàng Đáo cho thẻ ${cardId}:`, error);
      return false;
    }
  };

  // Function to generate warning message based on days remaining
  const getPaymentWarning = async (card: any) => {
    if (!card) return "";

    // Check if card is being held
    if (!isHoldingCard(card)) return "";

    try {
      // If card has "Đáo" orders in current month, return "Đã Đáo"
      const hasDaoOrder = await hasRecentDaoOrder(card);
      console.log(`[DEBUG] Thẻ ${card.id} có đơn hàng Đáo trong tháng: ${hasDaoOrder}`);

      if (hasDaoOrder) {
        return "Đã Đáo";
      }

      // Use the existing calculateDaysUntil method since it already works with our data structure
      const { days } = calculateDaysUntil(card.payment_date);

      if (days >= 1 && days <= 3) {
        return `Đến hạn ${days} ngày! Gấp`;
      } else if (days >= 5 && days <= 10) {
        return `Sắp đến hạn: còn ${days} ngày`;
      } else if (days >= 10 && days <= 20) {
        return `Còn ${days} ngày`;
      }

      return "";
    } catch (error) {
      console.error(`[ERROR] Lỗi khi kiểm tra cảnh báo thanh toán cho thẻ ${card.id}:`, error);
      return "";
    }
  };

  // Component để hiển thị cảnh báo thanh toán
  const PaymentWarningBadge = ({ card }: { card: any }) => {
    const [warning, setWarning] = useState<string>("");

    useEffect(() => {
      const fetchWarning = async () => {
        try {
          const warningText = await getPaymentWarning(card);
          setWarning(warningText);
        } catch (error) {
          console.error("Error fetching payment warning:", error);
          setWarning("");
        }
      };

      fetchWarning();
    }, [card]);

    if (!warning) return <div>-</div>;

    return (
      <div className="space-y-1">
        <Badge
          className={
            warning.includes("Gấp")
              ? "bg-red-100 text-red-800 border-red-200"
              : warning.includes("Đã Đáo")
                ? "bg-green-100 text-green-800 border-green-200"
                : "bg-amber-100 text-amber-800 border-amber-200"
          }
        >
          {warning}
        </Badge>
      </div>
    );
  };

  // Handle viewing card attachments
  const handleViewAttachments = (cardId: number, e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedCardId(cardId);
    setShowAttachmentDialog(true);
  };

  // Mobile Card View Component
  const MobileCardView = ({ customer }: { customer: CustomerType }) => {
    const outstandingFee = customer.outstandingFee !== undefined ? customer.outstandingFee : customer.outstanding_fee;

    const getOutstandingFeeColor = (fee: number) => {
      if (fee > 0) return "text-rose-500";
      if (fee < 0) return "text-emerald-600";
      return "text-muted-foreground";
    };

    return (
      <Card className="mb-2 shadow-sm cursor-pointer" onClick={() => handleViewDetail(customer.id.toString())}>
        <CardContent className="p-3">
          {/* Header: Customer Name + ID + Actions */}
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-2 flex-1 min-w-0 overflow-hidden">
              <span className="font-semibold text-sm truncate flex-shrink-0 min-w-0">{customer.name}</span>
              <Badge variant="outline" className="text-xs font-medium flex-shrink-0">
                #{customer.id}
              </Badge>
            </div>
            <div className="flex gap-1 ml-2 flex-shrink-0">
              <Button
                size="icon"
                variant="ghost"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditCustomer(customer.id.toString());
                }}
                className="h-7 w-7"
                aria-label="Sửa"
              >
                <Edit className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>

          {/* Info Row: Phone + Cards Count */}
          <div className="flex items-center justify-between text-xs">
            <div className="flex items-center gap-2">
              <Phone className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground truncate">
                {customer.phone || 'Chưa có SĐT'}
              </span>
            </div>
            <div className="flex items-center gap-1">
              <CreditCard className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">
                {countCustomerCards(customer)} thẻ
              </span>
            </div>
          </div>

          {/* Outstanding Fee Row */}
          <div className="flex items-center justify-between text-xs mt-2 pt-2 border-t">
            <div className="flex items-center gap-2">
              <DollarSign className="h-3 w-3 text-muted-foreground" />
              <span className="text-muted-foreground">Nợ phí:</span>
            </div>
            <span className={`font-medium ${getOutstandingFeeColor(outstandingFee || 0)}`}>
              {outstandingFee ? formatCurrency(outstandingFee) : '0'}
            </span>
          </div>

          {/* Note Row */}
          {customer.note && (
            <div className="mt-2 pt-2 border-t">
              <p className="text-xs text-muted-foreground truncate">
                <span className="font-medium">Ghi chú:</span> {customer.note}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // Render based on the current view mode
  const renderContent = () => {
    switch (viewMode) {
      case "detail":
        return (
          <CustomerDetail
            customerId={selectedCustomerId || ""}
            onBack={handleBackToList}
            onEdit={handleEditCustomer}
          />
        );
      case "form":
        return (
          <CustomerForm
            customerId={selectedCustomerId || undefined}
            onCancel={handleBackToList}
            onSuccess={handleCustomerFormSuccess}
          />
        );
      case "list":
      default:
        return (
          <div className="space-y-6">
            {/* Header Section */}
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
              <div>
                <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Quản lý khách hàng</h1>
                <p className="text-muted-foreground mt-1">
                  Quản lý thông tin khách hàng
                </p>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" onClick={() => setShowCustomerFilterModal(true)} className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  <span className="hidden sm:inline">Lọc khách hàng</span>
                </Button>
                <Button onClick={handleShowCreateCustomerModal}>
                  <Plus className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Thêm khách hàng</span>
                  <span className="sm:hidden">Thêm</span>
                </Button>
              </div>
            </div>

            {/* Search and Stats Section */}
            <div className="grid gap-4 grid-cols-1 md:grid-cols-5">
              <div className="md:col-span-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm khách hàng..."
                    className="pl-9"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Tổng KH</p>
                      <p className="text-2xl font-bold mt-1">{stats.totalCustomers}</p>
                    </div>
                    <User className="h-8 w-8 text-primary opacity-80" />
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm text-muted-foreground">Tổng nợ phí</p>
                      <p className="text-2xl font-bold mt-1 text-rose-500">
                        {formatCurrency(stats.totalOutstandingFee)}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-rose-500 opacity-80" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main Content */}
            <Card>
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div>
                    <CardTitle className="text-lg font-medium">Danh sách khách hàng</CardTitle>
                    <CardDescription className="mt-1">
                      Quản lý thông tin khách hàng
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">
                      {filteredCustomers.length} khách hàng
                    </Badge>
                    {customerFilters.hasOutstandingFee && (
                      <Badge variant="outline" className="py-1.5 px-3 text-xs">
                        Đang lọc
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-2 -mr-1"
                          onClick={() => setCustomerFilters({ hasOutstandingFee: false })}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                {/* Loading State */}
                {loading && (
                  <div className="flex justify-center items-center py-12">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    <span>Đang tải dữ liệu...</span>
                  </div>
                )}

                {/* Desktop Table View - Hidden on mobile */}
                {!loading && (
                  <div className="hidden lg:block">
                    <div className="rounded-lg border overflow-hidden">
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead className="w-[80px]">Mã KH</TableHead>
                              <TableHead>Khách hàng</TableHead>
                              <TableHead>Số thẻ</TableHead>
                              <TableHead>Nợ phí</TableHead>
                              <TableHead>Ghi chú</TableHead>
                              <TableHead className="text-right">Thao tác</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {filteredCustomers.map((customer) => (
                              <TableRow key={customer.id} className="group cursor-pointer hover:bg-muted/50 transition-colors" onClick={() => handleViewDetail(customer.id.toString())}>
                                <TableCell className="font-medium">
                                  {customer.id}
                                </TableCell>
                                <TableCell>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{customer.name}</span>
                                    <span className="text-xs text-muted-foreground">
                                      {customer.phone || 'Chưa có SĐT'}
                                    </span>
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-1">
                                    <span className="text-sm">{countCustomerCards(customer)}</span>
                                    <CreditCard className="h-4 w-4 text-muted-foreground" />
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {(() => {
                                    const outstandingFee = customer.outstandingFee !== undefined ? customer.outstandingFee : customer.outstanding_fee;

                                    if (outstandingFee > 0) {
                                      return (
                                        <span className="text-rose-500 font-medium">
                                          {formatCurrency(outstandingFee)}
                                        </span>
                                      );
                                    } else if (outstandingFee < 0) {
                                      return (
                                        <span className="text-emerald-600 font-medium">
                                          {formatCurrency(outstandingFee)}
                                        </span>
                                      );
                                    } else {
                                      return (
                                        <span className="text-muted-foreground">
                                          0
                                        </span>
                                      );
                                    }
                                  })()}
                                </TableCell>
                                <TableCell className="max-w-[200px] truncate">
                                  {customer.note || "—"}
                                </TableCell>
                                <TableCell className="text-right">
                                  <div className="flex items-center justify-end gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                    <Button
                                      size="icon"
                                      variant="ghost"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleEditCustomer(customer.id.toString());
                                      }}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </TableCell>
                              </TableRow>
                            ))}

                            {filteredCustomers.length === 0 && (
                              <TableRow>
                                <TableCell colSpan={6} className="h-24 text-center">
                                  <div className="flex flex-col items-center gap-2">
                                    <User className="h-8 w-8 text-muted-foreground" />
                                    <p className="text-muted-foreground">
                                      {searchTerm ? "Không tìm thấy khách hàng phù hợp" : "Chưa có khách hàng nào"}
                                    </p>
                                  </div>
                                </TableCell>
                              </TableRow>
                            )}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  </div>
                )}

                {/* Mobile Card View - Visible on mobile and tablet */}
                {!loading && (
                  <div className="lg:hidden">
                    {filteredCustomers.length > 0 ? (
                      <div className="space-y-4">
                        {filteredCustomers.map((customer) => (
                          <MobileCardView key={customer.id} customer={customer} />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-12">
                        <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                        <p className="text-muted-foreground">
                          {searchTerm ? "Không tìm thấy khách hàng phù hợp" : "Chưa có khách hàng nào"}
                        </p>
                      </div>
                    )}
                  </div>
                )}

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="mt-6">
                    {/* Mobile Pagination - Simple */}
                    <div className="flex md:hidden items-center justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1"
                      >
                        <span>Trước</span>
                      </Button>

                      <span className="text-sm text-muted-foreground">
                        Trang {currentPage} / {totalPages}
                      </span>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1"
                      >
                        <span>Sau</span>
                      </Button>
                    </div>

                    {/* Desktop Pagination - Full */}
                    <div className="hidden md:flex items-center justify-between">
                      <div className="text-sm text-muted-foreground">
                        Hiển thị {(currentPage - 1) * itemsPerPage + 1} - {Math.min(currentPage * itemsPerPage, totalItems)} trên tổng số {totalItems} khách hàng
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                        >
                          Trước
                        </Button>

                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          // Hiển thị 5 trang gần trang hiện tại
                          let pageToShow;
                          if (totalPages <= 5) {
                            pageToShow = i + 1;
                          } else if (currentPage <= 3) {
                            pageToShow = i + 1;
                          } else if (currentPage >= totalPages - 2) {
                            pageToShow = totalPages - 4 + i;
                          } else {
                            pageToShow = currentPage - 2 + i;
                          }

                          return (
                            <Button
                              key={pageToShow}
                              variant={currentPage === pageToShow ? "default" : "outline"}
                              size="sm"
                              onClick={() => setCurrentPage(pageToShow)}
                            >
                              {pageToShow}
                            </Button>
                          );
                        })}

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                          disabled={currentPage === totalPages}
                        >
                          Sau
                        </Button>
                      </div>
                    </div>
                  </div>
                )}
                </div>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  return (
    <Layout>
      <div className="container mx-auto py-6">
        {renderContent()}

        {/* Modal tạo khách hàng mới */}
        <Dialog open={showCreateCustomerModal} onOpenChange={setShowCreateCustomerModal}>
          <DialogContent className="md:max-w-[600px] max-h-[90vh] overflow-y-auto" aria-labelledby="create-customer-title" aria-describedby="create-customer-description">
            <CustomerCreateForm
              onCancel={() => setShowCreateCustomerModal(false)}
              onSuccess={handleCustomerFormSuccess}
            />
          </DialogContent>
        </Dialog>

        {renderEditCustomerModal()}

        {/* Filter Customers Dialog */}
        <Dialog open={showCustomerFilterModal} onOpenChange={setShowCustomerFilterModal}>
          <DialogContent className="sm:max-w-[500px]">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Lọc danh sách khách hàng
              </DialogTitle>
              <DialogDescription>
                Chọn các tiêu chí lọc để hiển thị danh sách khách hàng phù hợp
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-6 py-4">
              <div className="grid grid-cols-12 items-center gap-4">
                <div className="col-span-3 flex justify-end">
                  <Label htmlFor="outstandingFeeFilter" className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-rose-500" />
                    Nợ phí
                  </Label>
                </div>
                <div className="col-span-9">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="outstandingFeeFilter"
                      checked={customerFilters.hasOutstandingFee}
                      onCheckedChange={(checked: boolean | "indeterminate") => {
                        setCustomerFilters(prev => ({
                          ...prev,
                          hasOutstandingFee: checked === true
                        }));
                      }}
                    />
                    <Label htmlFor="outstandingFeeFilter">
                      Chỉ hiển thị khách hàng đang nợ phí
                    </Label>
                  </div>
                </div>
              </div>
            </div>
            <DialogFooter className="flex justify-between sm:justify-between">
              <Button
                onClick={() => {
                  setCustomerFilters({
                    hasOutstandingFee: false
                  });
                }}
                variant="outline"
                className="flex items-center gap-2"
              >
                <Trash2 className="h-4 w-4" />
                Xóa bộ lọc
              </Button>
              <Button
                onClick={() => setShowCustomerFilterModal(false)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                Áp dụng bộ lọc
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  );
};

export default Customers;
