import { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { CustomerCombobox } from "@/components/customers/CustomerCombobox";
import { Layout } from "@/components/layout/Layout";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon, Plus, Search, Pencil, Trash2, Loader2, Filter, X, ChevronDown, User, Building2, DollarSign, Receipt } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { CashReceivedFilterModal } from "@/components/cash-received/CashReceivedFilterModal";
import { toast } from "sonner";
import { CashReceived, getCashReceivedList, createCashReceived, deleteCashReceived, updateCashReceived } from "@/services/cashReceivedService";
import { POS, getPOSList } from "@/services/posService";
import { Customer, getCustomerList } from "@/services/customerService";
import { formatDateTimeVN } from "@/utils/date-format";

// Define the form interface
interface CashReceivedForm {
  id?: number;
  type: "POS" | "Customer" | "";
  pos_id?: number | null;
  customer_id?: number | null;
  amount: number | string;
  note: string;
}

export default function CashReceivedPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [showDialog, setShowDialog] = useState(false);
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const itemsPerPage = 10;

  // Data states
  const [cashReceivedList, setCashReceivedList] = useState<CashReceived[]>([]);
  const [filteredData, setFilteredData] = useState<CashReceived[]>([]);
  const [posList, setPosList] = useState<POS[]>([]);
  const [customerList, setCustomerList] = useState<Customer[]>([]);
  const [customerSearchTerm, setCustomerSearchTerm] = useState<string>('');
  const [isSearchingCustomers, setIsSearchingCustomers] = useState<boolean>(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const customerSearchRef = useRef<HTMLInputElement>(null);

  // Form state
  const [cashReceivedForm, setCashReceivedForm] = useState<CashReceivedForm>({
    type: "",
    pos_id: null,
    customer_id: null,
    amount: "",
    note: "",
  });

  // New state for type filter
  const [typeFilter, setTypeFilter] = useState<"all" | "pos" | "customer">("all");
  const [selectedPosId, setSelectedPosId] = useState<number | undefined>(undefined);
  const [selectedCustomerId, setSelectedCustomerId] = useState<number | undefined>(undefined);

  // State for date filters
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);

  // State for filter modal
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Handle type change
  const handleTypeChange = (value: string) => {
    setCashReceivedForm(prev => ({
      ...prev,
      type: value as "POS" | "Customer" | "",
      pos_id: value === "POS" ? prev.pos_id : null,
      customer_id: value === "Customer" ? prev.customer_id : null,
    }));

    // Reset selected customer when changing type
    if (value !== "Customer") {
      setSelectedCustomer(null);
    }
  };

  // Handle customer selection
  const handleCustomerSelect = (customer: Customer | null) => {
    setSelectedCustomer(customer);
  };

  // Thêm hàm định dạng số tiền trong phần cập nhật input
  const formatNumberInput = (value: string): string => {
    // Loại bỏ tất cả các ký tự không phải số
    const numericValue = value.replace(/[^\d]/g, '');
    // Chuyển đổi thành số và định dạng với dấu chấm phân cách hàng nghìn
    return numericValue === '' ? '' : new Intl.NumberFormat('vi-VN').format(parseInt(numericValue));
  };

  // Thêm hàm để loại bỏ định dạng số tiền khi submit
  const parseFormattedNumber = (value: string | number): number => {
    if (typeof value === 'number') return value;
    // Loại bỏ tất cả các ký tự không phải số
    const numericValue = value.replace(/\D/g, '');
    return numericValue === '' ? 0 : parseInt(numericValue);
  };

  // Cập nhật hàm xử lý thay đổi trường form
  const handleFormChange = (field: string, value: string | number) => {
    if (field === 'amount' && typeof value === 'string') {
      // Định dạng số tiền khi nhập vào trường amount
      setCashReceivedForm(prev => ({
        ...prev,
        [field]: formatNumberInput(value)
      }));
    } else {
      setCashReceivedForm(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    try {
      return formatDateTimeVN(dateString);
    } catch (error) {
      return dateString;
    }
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN').format(amount);
  };

  // Format outstanding fee with color
  const formatOutstandingFee = (amount: number | undefined) => {
    if (amount === undefined || amount === null) return '-';

    const formattedAmount = new Intl.NumberFormat('vi-VN').format(amount);

    if (amount > 0) {
      return <span className="text-red-500">{formattedAmount}</span>;
    } else if (amount < 0) {
      return <span className="text-green-500">{formattedAmount}</span>;
    } else {
      return <span className="text-gray-500">0</span>;
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Validation
    if (!cashReceivedForm.type) {
      toast.error("Vui lòng chọn loại phiếu thu");
      return;
    }

    if (cashReceivedForm.type === "POS" && !cashReceivedForm.pos_id) {
      toast.error("Vui lòng chọn POS");
      return;
    }

    if (cashReceivedForm.type === "Customer" && !cashReceivedForm.customer_id) {
      toast.error("Vui lòng chọn khách hàng");
      return;
    }

    if (!cashReceivedForm.amount || parseFormattedNumber(cashReceivedForm.amount) <= 0) {
      toast.error("Vui lòng nhập số tiền hợp lệ (lớn hơn 0)");
      return;
    }

    setIsSubmitting(true);

    try {
      const submitData = {
        pos_id: cashReceivedForm.type === "POS" ? cashReceivedForm.pos_id : null,
        customer_id: cashReceivedForm.type === "Customer" ? cashReceivedForm.customer_id : null,
        amount: parseFormattedNumber(cashReceivedForm.amount),
        note: cashReceivedForm.note,
        user_id: 1 // Default user ID
      };

      let response;

      if (editMode && cashReceivedForm.id) {
        // Update existing record
        response = await updateCashReceived(cashReceivedForm.id, submitData);
        if (response.success) {
          toast.success("Cập nhật phiếu thu thành công");
        } else {
          throw new Error(response.message || "Không thể cập nhật phiếu thu");
        }
      } else {
        // Create new record
        response = await createCashReceived(submitData);
        if (response.success) {
          toast.success("Tạo phiếu thu thành công");
        } else {
          throw new Error(response.message || "Không thể tạo phiếu thu");
        }
      }

      // Close dialog and refresh data
      setShowDialog(false);
      resetForm();
      fetchData();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error(error instanceof Error ? error.message : "Đã xảy ra lỗi khi xử lý phiếu thu");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Set page title
  useEffect(() => {
    document.title = "Quản lý Tiền về | Tín Phát Credit";
  }, []);

  // Fetch data from APIs
  const fetchData = async () => {
    setIsLoading(true);
    try {
      // Xây dựng các tham số tìm kiếm và lọc
      const params: any = {
        page: currentPage,
        limit: itemsPerPage
      };

      // Thêm tham số tìm kiếm nếu có
      if (searchTerm) {
        params.search = searchTerm;
      }

      // Thêm tham số lọc theo loại nếu có
      if (typeFilter !== 'all') {
        params.type = typeFilter;
      }

      // Thêm tham số lọc theo POS cụ thể nếu có
      if (typeFilter === 'pos' && selectedPosId !== undefined) {
        params.pos_id = selectedPosId.toString();
      }

      // Thêm tham số lọc theo Khách hàng cụ thể nếu có
      if (typeFilter === 'customer' && selectedCustomerId !== undefined) {
        params.customer_id = selectedCustomerId.toString();
      }

      // Thêm tham số lọc theo ngày nếu có
      if (startDate) {
        const formattedStartDate = format(startDate, 'yyyy-MM-dd');
        params.start_date = formattedStartDate;
        console.log('Formatted start_date:', formattedStartDate);
      }
      if (endDate) {
        const formattedEndDate = format(endDate, 'yyyy-MM-dd');
        params.end_date = formattedEndDate;
        console.log('Formatted end_date:', formattedEndDate);
      }

      // Get cash received list with pagination and filters
      const cashReceivedResponse = await getCashReceivedList(params.page, params.limit, params);
      if (cashReceivedResponse.success) {
        setCashReceivedList(cashReceivedResponse.data);
        setFilteredData(cashReceivedResponse.data);
        setTotalItems(cashReceivedResponse.total || 0);
        setTotalPages(cashReceivedResponse.totalPages || 1);
      } else {
        toast.error("Không thể tải danh sách phiếu thu");
      }

      // Get POS list
      const posResponse = await getPOSList();
      if (posResponse.success) {
        setPosList(posResponse.data);
      } else {
        toast.error("Không thể tải danh sách POS");
      }

      // Get Customer list - lấy 100 khách hàng đầu tiên
      const customerResponse = await getCustomerList(1, 100);
      if (customerResponse.success) {
        setCustomerList(customerResponse.data);
        console.log('CashReceived - Initial customers loaded:', customerResponse.data?.length || 0);
      } else {
        toast.error("Không thể tải danh sách khách hàng");
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Đã xảy ra lỗi khi tải dữ liệu");
    } finally {
      setIsLoading(false);
    }
  };

  // Load data on component mount and when page changes
  useEffect(() => {
    fetchData();
  }, [currentPage]);

  // Tải lại dữ liệu khi thay đổi bộ lọc
  useEffect(() => {
    // Reset về trang 1 khi thay đổi bộ lọc
    setCurrentPage(1);
    fetchData();
  }, [typeFilter, selectedPosId, selectedCustomerId, startDate, endDate]);

  // Reset filters
  const handleResetFilters = () => {
    // Reset các state
    setTypeFilter("all");
    setSelectedPosId(undefined);
    setSelectedCustomerId(undefined);
    setStartDate(undefined);
    setEndDate(undefined);
    setCurrentPage(1);

    // Không cần gọi fetchData ở đây vì useEffect sẽ tự động gọi khi các state thay đổi
  };

  // Handle applying filters from modal
  const handleApplyFilters = (filters: {
    type: "all" | "pos" | "customer";
    posId?: number;
    customerId?: number;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => {
    setTypeFilter(filters.type);
    setSelectedPosId(filters.posId);
    setSelectedCustomerId(filters.customerId);
    setStartDate(filters.startDate);
    setEndDate(filters.endDate);
    setCurrentPage(1);

    // Không cần setTimeout vì useEffect sẽ tự động gọi fetchData khi các state thay đổi
  };

  // Xử lý tìm kiếm
  const handleSearch = () => {
    // Reset về trang 1 khi tìm kiếm
    setCurrentPage(1);
    fetchData();
  };

  // Xử lý khi nhấn Enter trong ô tìm kiếm
  const handleSearchKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSearch();
    }
  };

  // Reset the form
  const resetForm = () => {
    setCashReceivedForm({
      type: "",
      pos_id: null,
      customer_id: null,
      amount: "",
      note: "",
    });
    setSelectedCustomer(null);
    setEditMode(false);
  };

  // Handle creating a new record
  const handleCreate = () => {
    resetForm();
    setEditMode(false);
    setShowDialog(true);
  };

  // Handle editing an existing record
  const handleEdit = (item: CashReceived) => {
    setCashReceivedForm({
      id: item.id,
      type: item.pos_id ? "POS" : item.customer_id ? "Customer" : "",
      pos_id: item.pos_id,
      customer_id: item.customer_id,
      amount: item.amount,
      note: item.note || "",
    });

    // Cập nhật selectedCustomer nếu là phiếu thu của khách hàng
    if (item.customer_id && item.customer) {
      setSelectedCustomer(item.customer);
    } else {
      setSelectedCustomer(null);
    }

    setEditMode(true);
    setShowDialog(true);
  };

  // Handle deleting a cash received
  const handleDelete = async (id: number) => {
    if (window.confirm("Bạn có chắc chắn muốn xóa phiếu thu này?")) {
      setIsLoading(true);
      try {
        const response = await deleteCashReceived(id);
        if (response.success) {
          toast.success("Xóa phiếu thu thành công");
          fetchData();
        } else {
          toast.error(response.message || "Không thể xóa phiếu thu");
        }
      } catch (error) {
        console.error("Error deleting cash received:", error);
        toast.error("Đã xảy ra lỗi khi xóa phiếu thu");
      } finally {
        setIsLoading(false);
      }
    }
  };

  // Find POS or Customer name based on ID
  const getEntityName = (item: CashReceived) => {
    if (item.pos_id) {
      const pos = posList.find(p => p.id === item.pos_id);
      return pos ? pos.name : `POS ID: ${item.pos_id}`;
    }
    if (item.customer_id) {
      const customer = customerList.find(c => c.id === item.customer_id);
      return customer ? customer.name : `Khách hàng ID: ${item.customer_id}`;
    }
    return "Không xác định";
  };

  // Dữ liệu đã được phân trang từ API

  // Cập nhật hàm getEntityType để trả về HTML với màu khác nhau cho POS và Khách hàng
  const getEntityType = (item: CashReceived) => {
    if (item.pos_id) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">POS</span>;
    } else if (item.customer_id) {
      return <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Khách hàng</span>;
    }
    return "-";
  };

  return (
    <Layout>
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-semibold">Quản lý Tiền về</h1>
        <Button onClick={handleCreate}>
          <Plus className="mr-2 h-4 w-4" />
          Thêm phiếu thu
        </Button>
      </div>

      {/* Modal bộ lọc */}
      <CashReceivedFilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        initialFilters={{
          type: typeFilter,
          posId: selectedPosId,
          customerId: selectedCustomerId,
          startDate,
          endDate
        }}
        posList={posList}
        customerList={customerList}
      />

      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Danh sách phiếu thu</CardTitle>
          <CardDescription>
            Quản lý thông tin phiếu thu tiền về từ POS và khách hàng
          </CardDescription>
          <div className="flex flex-wrap gap-2 justify-between items-center mt-2">
            <div className="flex w-full max-w-sm items-center space-x-2">
              <Input
                placeholder="Tìm kiếm phiếu thu..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={handleSearchKeyDown}
                className="max-w-[300px]"
              />
              <Button variant="outline" size="icon" onClick={handleSearch}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              {(typeFilter !== "all" || selectedPosId || selectedCustomerId || startDate || endDate) && (
                <Badge variant="outline" className="py-2 px-3">
                  Đang lọc
                  <Button
                    variant="ghost"
                    size="icon"
                    className="h-4 w-4 ml-2 -mr-1"
                    onClick={handleResetFilters}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              <Button
                variant="outline"
                size="icon"
                onClick={() => setShowFilterModal(true)}
              >
                <Filter className="h-4 w-4" />
                <span className="sr-only">Filter</span>
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center p-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[80px]">ID</TableHead>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>Loại</TableHead>
                    <TableHead>POS/Khách hàng</TableHead>
                    <TableHead className="text-right">Số tiền</TableHead>
                    <TableHead>Ghi chú</TableHead>
                    <TableHead className="text-right">Thao tác</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                        Không có dữ liệu phù hợp
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredData.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="font-medium">{item.id}</TableCell>
                        <TableCell>{formatDate(item.created_at || '')}</TableCell>
                        <TableCell>{getEntityType(item)}</TableCell>
                        <TableCell>{getEntityName(item)}</TableCell>
                        <TableCell className="text-right font-medium">
                          {formatCurrency(item.amount)}
                        </TableCell>
                        <TableCell>{item.note}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleEdit(item)}
                            >
                              <Pencil className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => handleDelete(item.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {totalPages > 1 && (() => {
                // Show fewer pages on mobile
                const isMobile = window.innerWidth < 768;
                const maxPagesToShow = isMobile ? 3 : 5;
                const startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
                const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
                const adjustedStartPage = Math.max(1, endPage - maxPagesToShow + 1);

                return (
                  <div className="mt-6">
                    {/* Mobile Pagination - Simple */}
                    <div className="flex md:hidden items-center justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                        disabled={currentPage === 1}
                        className="flex items-center gap-1"
                      >
                        <span>Trước</span>
                      </Button>

                      <span className="text-sm text-muted-foreground">
                        Trang {currentPage} / {totalPages}
                      </span>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                        disabled={currentPage === totalPages}
                        className="flex items-center gap-1"
                      >
                        <span>Sau</span>
                      </Button>
                    </div>

                    {/* Desktop Pagination - Full */}
                    <Pagination className="hidden md:flex">
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious
                            onClick={() => setCurrentPage((p) => Math.max(1, p - 1))}
                            className={currentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                            aria-disabled={currentPage === 1}
                            aria-label="Trang trước"
                          />
                        </PaginationItem>

                        {adjustedStartPage > 1 && (
                          <>
                            <PaginationItem>
                              <PaginationLink onClick={() => setCurrentPage(1)} className="cursor-pointer">
                                1
                              </PaginationLink>
                            </PaginationItem>
                            {adjustedStartPage > 2 && (
                              <PaginationItem>
                                <PaginationEllipsis />
                              </PaginationItem>
                            )}
                          </>
                        )}

                        {Array.from({ length: endPage - adjustedStartPage + 1 }, (_, i) => adjustedStartPage + i).map(page => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              onClick={() => setCurrentPage(page)}
                              isActive={currentPage === page}
                              className={currentPage !== page ? "cursor-pointer" : ""}
                              aria-current={currentPage === page ? "page" : undefined}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                        {endPage < totalPages && (
                          <>
                            {endPage < totalPages - 1 && (
                              <PaginationItem>
                                <PaginationEllipsis />
                              </PaginationItem>
                            )}
                            <PaginationItem>
                              <PaginationLink onClick={() => setCurrentPage(totalPages)} className="cursor-pointer">
                                {totalPages}
                              </PaginationLink>
                            </PaginationItem>
                          </>
                        )}

                        <PaginationItem>
                          <PaginationNext
                            onClick={() => setCurrentPage((p) => Math.min(totalPages, p + 1))}
                            className={currentPage === totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                            aria-disabled={currentPage === totalPages}
                            aria-label="Trang kế tiếp"
                          />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                );
              })()}
            </>
          )}
        </CardContent>
      </Card>

      {/* Dialog for creating/editing cash received */}
      <Dialog open={showDialog} onOpenChange={setShowDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>
              {editMode ? "Chỉnh sửa phiếu thu" : "Thêm phiếu thu mới"}
            </DialogTitle>
            <DialogDescription>
              Nhập thông tin chi tiết về phiếu thu tiền về
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="type" className="text-right">
                Loại
              </Label>
              <div className="col-span-3">
                <Select
                  value={cashReceivedForm.type}
                  onValueChange={handleTypeChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Chọn loại" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="POS">POS</SelectItem>
                    <SelectItem value="Customer">Khách hàng</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            {cashReceivedForm.type === "POS" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="pos_id" className="text-right">
                  POS
                </Label>
                <div className="col-span-3">
                  <Select
                    value={cashReceivedForm.pos_id?.toString() || ""}
                    onValueChange={(value) => handleFormChange("pos_id", Number(value))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Chọn POS" />
                    </SelectTrigger>
                    <SelectContent>
                      {posList
                        .filter(pos => pos.status !== 'locked') // Chỉ hiển thị POS có trạng thái khác 'locked'
                        .map((pos) => (
                          <SelectItem key={pos.id} value={pos.id.toString()}>
                            {pos.name}
                          </SelectItem>
                        ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
            {cashReceivedForm.type === "Customer" && (
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="customer_id" className="text-right">
                  Khách hàng
                </Label>
                <div className="col-span-3">
                  <div className="space-y-1">
                    <CustomerCombobox
                      value={cashReceivedForm.customer_id}
                      onChange={(value) => handleFormChange("customer_id", value)}
                      onCustomerSelect={handleCustomerSelect}
                      placeholder="Chọn khách hàng"
                    />
                    {selectedCustomer && selectedCustomer.outstanding_fee !== undefined && (
                      <div className="text-xs">
                        Số tiền đang nợ: {formatOutstandingFee(selectedCustomer.outstanding_fee)}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="amount" className="text-right">
                Số tiền
              </Label>
              <div className="col-span-3">
                <Input
                  id="amount"
                  type="text"
                  value={cashReceivedForm.amount}
                  onChange={(e) => handleFormChange("amount", e.target.value)}
                  placeholder="Nhập số tiền"
                />
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="note" className="text-right">
                Ghi chú
              </Label>
              <div className="col-span-3">
                <Input
                  id="note"
                  value={cashReceivedForm.note}
                  onChange={(e) => handleFormChange("note", e.target.value)}
                  placeholder="Nhập ghi chú"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setShowDialog(false)}>
              Hủy
            </Button>
            <Button type="button" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {editMode ? "Cập nhật" : "Tạo mới"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
}
