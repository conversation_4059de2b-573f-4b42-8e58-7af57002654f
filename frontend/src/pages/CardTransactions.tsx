import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Search, Filter, Trash2, Loader2, X, CreditCard, Calendar, User, Building2, DollarSign } from "lucide-react";
import { format } from "date-fns";
import { formatDateTimeVN } from "@/utils/date-format";

// Components
import { Layout } from "@/components/layout/Layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { CardTransactionsFilterModal } from "@/components/card-transactions/CardTransactionsFilterModal";

// Services
import { CardSwipe, deleteCardSwipe } from "@/services/cardService";
import { getPOSList, POS } from "@/services/posService";
import { getBankList, Bank } from "@/services/bankService";
import apiClient from "@/lib/apiClient";

export default function CardTransactions() {
  const [searchTerm, setSearchTerm] = useState("");
  const [cardSwipes, setCardSwipes] = useState<CardSwipe[]>([]);
  const [filteredSwipes, setFilteredSwipes] = useState<CardSwipe[]>([]);
  const [loading, setLoading] = useState(true);

  // State for POS list
  const [posList, setPosList] = useState<POS[]>([]);
  const [loadingPosList, setLoadingPosList] = useState(false);

  // State for Bank list
  const [bankList, setBankList] = useState<Bank[]>([]);
  const [loadingBankList, setLoadingBankList] = useState(false);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalSwipes, setTotalSwipes] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const pageSize = 20;

  // State for filters
  const [selectedPosId, setSelectedPosId] = useState<string>("all");
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();
  const [showFilterModal, setShowFilterModal] = useState(false);

  // Add page title
  useEffect(() => {
    document.title = "Quản lý quẹt thẻ | Tín Phát Credit";
  }, []);

  // Fetch POS and Bank list on mount
  useEffect(() => {
    const fetchData = async () => {
      // Fetch POS
      setLoadingPosList(true);
      try {
        const posData = await getPOSList();
        setPosList(posData.data || []);
      } catch (error) {
        console.error('Lỗi khi tải danh sách máy POS:', error);
        toast.error('Không thể tải danh sách máy POS');
      } finally {
        setLoadingPosList(false);
      }

      // Fetch Banks
      setLoadingBankList(true);
      try {
        const bankData = await getBankList();
        if (bankData && bankData.success && Array.isArray(bankData.data)) {
          setBankList(bankData.data);
        } else {
          console.error("Failed to fetch banks or invalid data format:", bankData);
          setBankList([]);
          toast.error('Không thể tải danh sách ngân hàng hoặc dữ liệu không hợp lệ');
        }
      } catch (error) {
        console.error('Lỗi khi tải danh sách ngân hàng:', error);
        setBankList([]);
        toast.error('Không thể tải danh sách ngân hàng');
      } finally {
        setLoadingBankList(false);
      }
    };
    fetchData();
  }, []);

  // Fetch card swipes from API, triggered by page change or filter changes
  useEffect(() => {
    fetchCardSwipes();
  }, [currentPage, selectedPosId, startDate, endDate]);

  const fetchCardSwipes = async () => {
    setLoading(true);
    try {
      // Build query parameters
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: pageSize.toString(),
        sort: 'id',
        order: 'desc'
      });

      if (selectedPosId && selectedPosId !== "all") {
        params.append('posId', selectedPosId);
      }
      if (startDate) {
        params.append('startDate', format(startDate, 'yyyy-MM-dd'));
      }
      if (endDate) {
        // Thêm 1 ngày vào end_date để bao gồm cả ngày kết thúc
        const nextDay = new Date(endDate);
        nextDay.setDate(nextDay.getDate() + 1);
        params.append('endDate', format(nextDay, 'yyyy-MM-dd'));
      }

      const response = await apiClient.get(`/api/swipes?${params.toString()}`);
      setCardSwipes(response.data.data);
      setFilteredSwipes(response.data.data);
      setTotalSwipes(response.data.total || 0);
      setTotalPages(response.data.totalPages || 1);
    } catch (error) {
      console.error('Lỗi khi tải dữ liệu quẹt thẻ:', error);
      toast.error('Không thể tải dữ liệu giao dịch quẹt thẻ');
    } finally {
      setLoading(false);
    }
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setSelectedPosId("all");
    setStartDate(undefined);
    setEndDate(undefined);
    setCurrentPage(1);
    // Không gọi fetchCardSwipes() ở đây vì useEffect sẽ tự động gọi khi các state thay đổi
  };

  // Handle applying filters from modal
  const handleApplyFilters = (filters: {
    posId: string;
    startDate: Date | undefined;
    endDate: Date | undefined;
  }) => {
    setSelectedPosId(filters.posId);
    setStartDate(filters.startDate);
    setEndDate(filters.endDate);
    setCurrentPage(1);
    // Không cần gọi fetchCardSwipes() ở đây vì useEffect sẽ tự động gọi khi các state thay đổi
  };

  // Filter card swipes based on search term
  useEffect(() => {
    if (!searchTerm) {
      setFilteredSwipes(cardSwipes);
      return;
    }

    const lowercaseSearch = searchTerm.toLowerCase();
    const filtered = cardSwipes.filter(
      swipe =>
        swipe.id.toString().includes(lowercaseSearch) ||
        (swipe.Card?.Customer?.name || '').toLowerCase().includes(lowercaseSearch) ||
        (swipe.Card?.card_number || '').toLowerCase().includes(lowercaseSearch) ||
        // Include bank name in search
        (getBankName(swipe.Card?.bank_id) || '').toLowerCase().includes(lowercaseSearch) ||
        swipe.order_id.toString().includes(lowercaseSearch) ||
        (getPOSName(swipe.pos_id) || '').toLowerCase().includes(lowercaseSearch)
    );

    setFilteredSwipes(filtered);
  }, [searchTerm, cardSwipes, posList, bankList]); // Add bankList dependency

  // Handle deleting a card swipe
  const handleDeleteSwipe = async (swipeId: number) => {
    if (confirm('Bạn có chắc chắn muốn xóa giao dịch quẹt thẻ này?')) {
      try {
        await deleteCardSwipe(swipeId);
        toast.success('Đã xóa giao dịch quẹt thẻ thành công');
        if (filteredSwipes.length === 1 && currentPage > 1) {
          setCurrentPage(currentPage - 1);
        } else {
          fetchCardSwipes();
        }
      } catch (error) {
        console.error('Lỗi khi xóa giao dịch quẹt thẻ:', error);
        toast.error('Không thể xóa giao dịch quẹt thẻ');
      }
    }
  };

  // Manejo de cambio de página (Function to handle page changes)
  const handlePageChange = (page: number) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // Format currency
  const formatCurrency = (amount: number | string | null | undefined): string => {
    if (amount === null || amount === undefined) return '0';
    const numAmount = Number(amount);
    if (isNaN(numAmount)) return '0';
    return Math.round(numAmount).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
  };

  // Format date
  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return formatDateTimeVN(dateString);
    } catch (e) {
      console.error("Error formatting date:", e);
      return 'Lỗi định dạng';
    }
  };

  // Helper function to get POS Name
  const getPOSName = (posId: number | undefined | null): string => {
    if (posId === null || posId === undefined) return 'Không rõ';
    if (loadingPosList) return 'Đang tải POS...'; // Indicate loading
    const pos = posList.find(p => p.id === posId);
    return pos ? pos.name : `POS ID: ${posId}`; // Fallback to ID if not found
  };

  // Helper function to get Bank Name
  const getBankName = (bankId: number | undefined | null): string => {
    if (bankId === null || bankId === undefined) return 'Không rõ';
    if (loadingBankList) return 'Đang tải NH...';
    const bank = bankList.find(b => b.id === bankId);
    // Return bank name or a fallback if not found
    return bank ? bank.name : `Không tìm thấy NH`;
  };

  // Render pagination controls with responsive design
  const renderPagination = () => {
    if (totalPages <= 1) return null;

    // Show fewer pages on mobile
    const isMobile = window.innerWidth < 768;
    const maxPagesToShow = isMobile ? 3 : 5;
    const startPage = Math.max(1, currentPage - Math.floor(maxPagesToShow / 2));
    const endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    const adjustedStartPage = Math.max(1, endPage - maxPagesToShow + 1);

    return (
      <div className="mt-6">
        {/* Mobile Pagination - Simple */}
        <div className="flex md:hidden items-center justify-between">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="flex items-center gap-1"
          >
            <span>Trước</span>
          </Button>

          <span className="text-sm text-muted-foreground">
            Trang {currentPage} / {totalPages}
          </span>

          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="flex items-center gap-1"
          >
            <span>Sau</span>
          </Button>
        </div>

        {/* Desktop Pagination - Full */}
        <Pagination className="hidden md:flex">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                onClick={() => handlePageChange(currentPage - 1)}
                className={currentPage === 1 ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                aria-disabled={currentPage === 1}
                aria-label="Trang trước"
              />
            </PaginationItem>

            {adjustedStartPage > 1 && (
              <>
                <PaginationItem>
                  <PaginationLink onClick={() => handlePageChange(1)} className="cursor-pointer">
                    1
                  </PaginationLink>
                </PaginationItem>
                {adjustedStartPage > 2 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
              </>
            )}

            {Array.from({ length: endPage - adjustedStartPage + 1 }, (_, i) => adjustedStartPage + i).map(page => (
              <PaginationItem key={page}>
                <PaginationLink
                  onClick={() => handlePageChange(page)}
                  isActive={currentPage === page}
                  className={currentPage !== page ? "cursor-pointer" : ""}
                  aria-current={currentPage === page ? "page" : undefined}
                >
                  {page}
                </PaginationLink>
              </PaginationItem>
            ))}

            {endPage < totalPages && (
              <>
                {endPage < totalPages - 1 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
                <PaginationItem>
                  <PaginationLink onClick={() => handlePageChange(totalPages)} className="cursor-pointer">
                    {totalPages}
                  </PaginationLink>
                </PaginationItem>
              </>
            )}

            <PaginationItem>
              <PaginationNext
                onClick={() => handlePageChange(currentPage + 1)}
                className={currentPage === totalPages ? "pointer-events-none opacity-50 cursor-not-allowed" : "cursor-pointer"}
                aria-disabled={currentPage === totalPages}
                aria-label="Trang kế tiếp"
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      </div>
    );
  };

  // Mobile Card View Component
  const MobileCardView = ({ swipe }: { swipe: CardSwipe }) => (
    <Card className="mb-3 shadow-sm">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-3">
          <div className="flex items-center gap-2 flex-wrap">
            <Badge variant="outline" className="text-xs font-medium">
              QT#{swipe.id}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              #{swipe.order_id}
            </Badge>
          </div>
          <Button
            size="icon"
            variant="ghost"
            onClick={() => handleDeleteSwipe(swipe.id)}
            className="text-destructive hover:text-destructive hover:bg-destructive/10 h-8 w-8 flex-shrink-0"
            aria-label="Xóa"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>

        <div className="space-y-3">
          {/* Customer and Card Info */}
          <div className="flex items-start gap-3">
            <User className="h-4 w-4 text-muted-foreground mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <p className="font-medium text-sm leading-tight">
                {swipe.Card?.Customer?.name || 'Không có tên'}
              </p>
              <p className="text-xs text-muted-foreground mt-1 leading-tight">
                {swipe.Card?.card_number || 'Không có thẻ'}
                {swipe.Card?.bank_id && (
                  <span className="inline-block ml-1 px-1.5 py-0.5 bg-primary/10 text-primary rounded text-xs">
                    {getBankName(swipe.Card.bank_id)}
                  </span>
                )}
              </p>
            </div>
          </div>

          {/* Date and POS */}
          <div className="grid grid-cols-1 gap-2">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-xs text-muted-foreground">
                {formatDate(swipe.swipe_date)}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <span className="text-xs text-muted-foreground">
                {getPOSName(swipe.pos_id)}
              </span>
            </div>
          </div>

          {/* Amount Info */}
          <div className="pt-3 border-t bg-muted/30 -mx-4 px-4 py-3 rounded-b-lg">
            <div className="flex items-center justify-between text-sm">
              <div className="flex items-center gap-1">
                <span className="text-muted-foreground text-xs">Số tiền:</span>
                <span className="font-semibold">{formatCurrency(swipe.amount)}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-muted-foreground text-xs">Phí:</span>
                <span className="font-semibold text-rose-500">{formatCurrency(swipe.bank_fee)}</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-muted-foreground text-xs">Thực nhận:</span>
                <span className="font-semibold text-emerald-600">{formatCurrency(swipe.net_amount)}</span>
              </div>
            </div>
          </div>

          {/* Note */}
          {swipe.note && (
            <div className="pt-2 border-t">
              <p className="text-xs text-muted-foreground">
                <span className="font-medium">Ghi chú:</span> {swipe.note}
              </p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );

  // Simplify renderContent to always show the list
  const renderListContent = () => {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader className="pb-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
              <div>
                <CardTitle className="text-lg font-medium">Giao dịch quẹt thẻ</CardTitle>
                <CardDescription className="mt-1">
                  Quản lý các giao dịch quẹt thẻ và theo dõi phí ngân hàng
                </CardDescription>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">
                  {totalSwipes} giao dịch
                </Badge>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col gap-4 mb-6">
              {/* Search and Filter Controls */}
              <div className="flex flex-col gap-3">
                {/* Search Bar */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Tìm kiếm theo mã, khách hàng, số thẻ..."
                    className="pl-9 h-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                {/* Filter Controls */}
                <div className="flex items-center justify-between gap-3">
                  <div className="flex items-center gap-2 flex-1">
                    {(selectedPosId !== "all" || startDate || endDate) && (
                      <Badge variant="outline" className="py-1.5 px-3 text-xs">
                        Đang lọc
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-4 w-4 ml-2 -mr-1"
                          onClick={handleResetFilters}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </Badge>
                    )}
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowFilterModal(true)}
                    className="flex items-center gap-2 px-3"
                  >
                    <Filter className="h-4 w-4" />
                    <span className="hidden sm:inline">Bộ lọc</span>
                  </Button>
                </div>
              </div>
            </div>

            {/* Loading State */}
            {loading && (
              <div className="flex justify-center items-center py-12">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <span>Đang tải dữ liệu...</span>
              </div>
            )}

            {/* Desktop Table View - Hidden on mobile */}
            {!loading && (
              <div className="hidden lg:block">
                <div className="rounded-lg border overflow-hidden">
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[80px]">Mã quẹt</TableHead>
                          <TableHead>Thời gian</TableHead>
                          <TableHead>Mã đơn</TableHead>
                          <TableHead>Khách hàng / Thẻ</TableHead>
                          <TableHead>Tên POS</TableHead>
                          <TableHead>Số tiền</TableHead>
                          <TableHead>Phí bank</TableHead>
                          <TableHead>Số tiền về</TableHead>
                          <TableHead>Ghi chú</TableHead>
                          <TableHead className="text-right w-[100px]">Thao tác</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {filteredSwipes.map((swipe) => (
                          <TableRow key={swipe.id} className="group hover:bg-muted/50 transition-colors">
                            <TableCell className="font-medium">{swipe.id}</TableCell>
                            <TableCell>{formatDate(swipe.swipe_date)}</TableCell>
                            <TableCell>
                              <Badge variant="outline">#{swipe.order_id}</Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-col">
                                <span>{swipe.Card?.Customer?.name || 'Không có tên'}</span>
                                <span className="text-xs text-muted-foreground">
                                  {swipe.Card?.card_number || 'Không có thẻ'}
                                  {swipe.Card?.bank_id &&
                                    ` - ${getBankName(swipe.Card.bank_id)}`
                                  }
                                </span>
                              </div>
                            </TableCell>
                            <TableCell>
                              {getPOSName(swipe.pos_id)}
                            </TableCell>
                            <TableCell>{formatCurrency(swipe.amount)}</TableCell>
                            <TableCell className="text-rose-500">{formatCurrency(swipe.bank_fee)}</TableCell>
                            <TableCell className="text-emerald-600">{formatCurrency(swipe.net_amount)}</TableCell>
                            <TableCell>{swipe.note || "—"}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex items-center justify-end gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  onClick={() => handleDeleteSwipe(swipe.id)}
                                  className="text-destructive hover:text-destructive hover:bg-destructive/10"
                                  aria-label="Xóa"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        ))}

                        {filteredSwipes.length === 0 && (
                          <TableRow>
                            <TableCell colSpan={10} className="text-center py-6 text-muted-foreground">
                              {searchTerm ? 'Không tìm thấy giao dịch nào phù hợp.' : 'Không có giao dịch quẹt thẻ nào.'}
                            </TableCell>
                          </TableRow>
                        )}
                      </TableBody>
                    </Table>
                  </div>
                </div>
              </div>
            )}

            {/* Mobile Card View - Visible on mobile and tablet */}
            {!loading && (
              <div className="lg:hidden">
                {filteredSwipes.length > 0 ? (
                  <div className="space-y-4">
                    {filteredSwipes.map((swipe) => (
                      <MobileCardView key={swipe.id} swipe={swipe} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <CreditCard className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">
                      {searchTerm ? 'Không tìm thấy giao dịch nào phù hợp.' : 'Không có giao dịch quẹt thẻ nào.'}
                    </p>
                  </div>
                )}
              </div>
            )}

            {/* Pagination */}
            {!loading && totalPages > 1 && renderPagination()}
          </CardContent>
        </Card>
      </div>
    );
  };

  return (
    <Layout>
      {renderListContent()}

      {/* Modal bộ lọc */}
      <CardTransactionsFilterModal
        isOpen={showFilterModal}
        onClose={() => setShowFilterModal(false)}
        onApply={handleApplyFilters}
        initialFilters={{
          posId: selectedPosId,
          startDate,
          endDate
        }}
        posList={posList}
      />
    </Layout>
  );
}