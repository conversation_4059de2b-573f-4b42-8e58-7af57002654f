import { useState, useEffect } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  Card<PERSON><PERSON><PERSON>,
  CardFooter,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { format, startOfMonth, endOfMonth, eachDayOfInterval, addMonths, subMonths, getMonth, getYear, parseISO, startOfDay, endOfDay } from "date-fns";
import { vi } from "date-fns/locale";
import { Calendar as CalendarIcon, Filter, ChevronLeft, ChevronRight, <PERSON><PERSON><PERSON>, Edit, X, Check, DollarSign, TrendingUp, TrendingDown, FileText } from "lucide-react";
import { cn, formatCurrency, parseCurrency } from "@/lib/utils";
import { getFinancialReportByDate, getFinancialReportsByDateRange, createFinancialReport, updateFinancialReport } from "@/services/financialReportService";
import { clearReportCache } from "@/services/cacheService";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";

// Định nghĩa kiểu dữ liệu cho báo cáo từ API
interface FinancialReportData {
  id?: number;
  report_date: string;
  opening_balance: number;
  total_income: number;
  total_transfer: number;
  closing_balance: number;
  total_returned: number;
  total_swiped: number;
  unprocessed_amount: number;
  profit: number;
  note?: string;
}

// Định nghĩa kiểu dữ liệu cho báo cáo hiển thị
type FinancialDataItem = {
  id?: number;
  date?: string;
  weekId?: string;
  monthId?: string;
  startDate?: string;
  endDate?: string;
  month?: string;
  openingBalance: string;
  totalIncome: string;
  totalTransfer: string;
  closingBalance: string;
  receivedAmount: string;
  totalSwiped: string;
  unswipedAmount: string;
  profit: string;
  notes: string;
};

// Chuyển đổi dữ liệu từ API sang định dạng hiển thị
const mapReportToDisplayData = (report: FinancialReportData): FinancialDataItem => {
  // Đảm bảo các giá trị số không bị null hoặc undefined
  const safeNumber = (value: any): number => {
    if (value === null || value === undefined) return 0;
    if (typeof value === 'string') return parseFloat(value.replace(/[^\d.-]/g, '')) || 0;
    return Number(value) || 0;
  };

  return {
    id: report.id,
    date: format(parseISO(report.report_date), 'dd/MM/yyyy'),
    openingBalance: safeNumber(report.opening_balance).toString(),
    totalIncome: safeNumber(report.total_income).toString(),
    totalTransfer: safeNumber(report.total_transfer).toString(),
    closingBalance: safeNumber(report.closing_balance).toString(),
    receivedAmount: safeNumber(report.total_returned).toString(),
    totalSwiped: safeNumber(report.total_swiped).toString(),
    unswipedAmount: safeNumber(report.unprocessed_amount).toString(),
    profit: safeNumber(report.profit).toString(),
    notes: report.note || ""
  };
};

// Tính tổng các giá trị
const calculateTotals = (data: FinancialDataItem[]) => {
  const totals = data.reduce(
    (acc, item) => {
      acc.totalIncome += parseFloat(item.totalIncome);
      acc.totalTransfer += parseFloat(item.totalTransfer);
      acc.receivedAmount += parseFloat(item.receivedAmount);
      acc.totalSwiped += parseFloat(item.totalSwiped);
      acc.unswipedAmount += parseFloat(item.unswipedAmount);
      acc.profit += parseFloat(item.profit);
      return acc;
    },
    {
      totalIncome: 0,
      totalTransfer: 0,
      receivedAmount: 0,
      totalSwiped: 0,
      unswipedAmount: 0,
      profit: 0,
    }
  );

  return totals;
};

export function FinancialReport() {
  // Thay đổi state date thành startDate và endDate
  const [startDate, setStartDate] = useState<Date | undefined>(startOfMonth(new Date()));
  const [endDate, setEndDate] = useState<Date | undefined>(endOfMonth(new Date()));
  const [reportType, setReportType] = useState("daily"); // Báo cáo ngày là mặc định
  const [filteredData, setFilteredData] = useState<FinancialDataItem[]>([]);
  // Thay đổi editingDate thành editingItemId để lưu ID
  const [editingItemId, setEditingItemId] = useState<number | null>(null);
  const [editableOpeningBalance, setEditableOpeningBalance] = useState("");
  const [periodLabel, setPeriodLabel] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingReportData, setPendingReportData] = useState<any>(null);
  // Thay đổi editingNote thành editingNoteItemId
  const [editingNoteItemId, setEditingNoteItemId] = useState<number | null>(null);
  const [editableNote, setEditableNote] = useState("");

  // Tính tổng các giá trị
  const totals = calculateTotals(filteredData);

  // Cập nhật dữ liệu dựa trên loại báo cáo và khoảng ngày
  useEffect(() => {
    // Chỉ chạy khi có cả startDate và endDate
    if (!startDate || !endDate) return;
    updateReportData();
  }, [startDate, endDate, reportType]);



  const fetchDateRangeReports = async (fetchStartDate: Date, fetchEndDate: Date) => {
    try {
      setIsLoading(true);
      const formattedStartDate = format(fetchStartDate, 'yyyy-MM-dd');
      const formattedEndDate = format(fetchEndDate, 'yyyy-MM-dd');
      const response = await getFinancialReportsByDateRange(formattedStartDate, formattedEndDate);

      if (response.success && response.data && response.data.length > 0) {
        const dailyData = response.data.map(mapReportToDisplayData);

        if (reportType === "monthly") {
          // Tính trung bình số dư đầu kỳ của các ngày trong tháng
          const averageOpeningBalance = dailyData.reduce((sum, item) => sum + parseFloat(item.openingBalance), 0) / dailyData.length;

          const monthData: FinancialDataItem = {
            date: `Tháng ${getMonth(fetchStartDate) + 1}/${getYear(fetchStartDate)}`,
            monthId: `month-${format(fetchStartDate, 'yyyy-MM')}`,
            startDate: format(fetchStartDate, 'yyyy-MM-dd'),
            endDate: format(fetchEndDate, 'yyyy-MM-dd'),
            month: format(fetchStartDate, 'MM/yyyy'),
            openingBalance: averageOpeningBalance.toString(),
            totalIncome: dailyData.reduce((sum, item) => sum + parseFloat(item.totalIncome), 0).toString(),
            totalTransfer: dailyData.reduce((sum, item) => sum + parseFloat(item.totalTransfer), 0).toString(),
            closingBalance: dailyData[dailyData.length - 1]?.closingBalance || "0",
            receivedAmount: dailyData.reduce((sum, item) => sum + parseFloat(item.receivedAmount), 0).toString(),
            totalSwiped: dailyData.reduce((sum, item) => sum + parseFloat(item.totalSwiped), 0).toString(),
            unswipedAmount: dailyData.reduce((sum, item) => sum + parseFloat(item.unswipedAmount), 0).toString(),
            profit: dailyData.reduce((sum, item) => sum + parseFloat(item.profit), 0).toString(),
            notes: ""
          };
          setFilteredData([monthData]);
        } else {
          setFilteredData(dailyData);
        }
      } else {
        setFilteredData([]);
      }
    } catch (error: any) {
      console.error("Lỗi khi lấy dữ liệu báo cáo:", error);
      const errorMessage = error.response?.status === 404
        ? "API endpoint không tồn tại"
        : "Không thể lấy dữ liệu báo cáo";
      toast.error(errorMessage);
      setFilteredData([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Cập nhật dữ liệu báo cáo dựa trên loại báo cáo và khoảng ngày
  const updateReportData = () => {
    if (!startDate || !endDate) return;

    if (reportType === "daily") {
      setPeriodLabel(`Từ ${format(startDate, "dd/MM/yyyy")} đến ${format(endDate, "dd/MM/yyyy")}`);
      fetchDateRangeReports(startOfDay(startDate), endOfDay(endDate));
    } else if (reportType === "monthly") {
      const monthStart = startOfMonth(startDate);
      const monthEnd = endOfMonth(startDate);
      setPeriodLabel(`Tháng ${format(startDate, "MM/yyyy")}`);
      fetchDateRangeReports(monthStart, monthEnd);
    }
  };

  // Xử lý thay đổi loại báo cáo
  const handleReportTypeChange = (value: string) => {
    setReportType(value);
    // Reset ngày về tháng hiện tại khi đổi loại báo cáo
    const now = new Date();
    setStartDate(startOfMonth(now));
    setEndDate(endOfMonth(now));
  };

  // Xử lý thay đổi ngày bắt đầu
  const handleStartDateChange = (date: Date | undefined) => {
    if (!date) return;
    setStartDate(date);
    // Nếu là báo cáo tháng, tự động cập nhật endDate
    if (reportType === "monthly") {
      setEndDate(endOfMonth(date));
    }
  };

  // Xử lý thay đổi ngày kết thúc
  const handleEndDateChange = (date: Date | undefined) => {
    if (!date) return;
    setEndDate(date);
  };

  // Thêm hàm xử lý chọn tháng
  const handleMonthChange = (date: Date | undefined) => {
    if (!date) return;
    const monthStart = startOfMonth(date);
    const monthEnd = endOfMonth(date);
    setStartDate(monthStart);
    setEndDate(monthEnd);
  };

  // Xử lý chỉnh sửa số dư đầu ngày - dùng ID
  const handleEditOpeningBalance = (itemId: number | undefined) => {
    if (itemId === undefined) return;
    setEditingItemId(itemId);
    const item = filteredData.find(item => item.id === itemId);
    if (item) {
      setEditableOpeningBalance(item.openingBalance.replace(/[^\d]/g, ""));
    }
  };

  // Xử lý chỉnh sửa ghi chú - dùng ID
  const handleEditNote = (itemId: number | undefined) => {
     if (itemId === undefined) return;
    setEditingNoteItemId(itemId);
    const item = filteredData.find(item => item.id === itemId);
    if (item) {
      setEditableNote(item.notes || '');
    }
  };

  // Xử lý lưu ghi chú
  const handleSaveNote = async () => {
    if (editingNoteItemId === null) return;

    try {
      const item = filteredData.find(i => i.id === editingNoteItemId);
      if (!item || !item.date) return;

      const dateParts = item.date.split('/');
      const reportDateStr = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

      const reportData: any = {
        report_date: reportDateStr,
        note: editableNote,
        user_id: 1
      };

      await updateFinancialReport(editingNoteItemId, reportData);
      console.log("Cập nhật ghi chú cho báo cáo có ID:", editingNoteItemId);

      // Lấy lại dữ liệu mới từ server để cập nhật tất cả các trường
      const updatedReport = await getFinancialReportByDate(reportDateStr);

      if (updatedReport.success && updatedReport.data) {
        const displayData = mapReportToDisplayData(updatedReport.data);
        const updatedData = filteredData.map(dataItem => {
          if (dataItem.id === editingNoteItemId) {
            return displayData;
          }
          return dataItem;
        });
        setFilteredData(updatedData);
        toast.success("Đã cập nhật ghi chú");
      } else {
        // Nếu không lấy được dữ liệu mới, chỉ cập nhật trường ghi chú
        const updatedData = filteredData.map(dataItem => {
          if (dataItem.id === editingNoteItemId) {
            return { ...dataItem, notes: editableNote };
          }
          return dataItem;
        });
        setFilteredData(updatedData);
        toast.success("Đã cập nhật ghi chú");
      }

      setEditingNoteItemId(null);
      setEditableNote("");
    } catch (error) {
      console.error("Lỗi khi cập nhật ghi chú:", error);
      toast.error("Không thể cập nhật ghi chú");
    }
  };

  // Định dạng số tiền khi nhập với dấu chấm phân cách hàng nghìn
  const formatNumberInput = (value: string) => {
    // Loại bỏ tất cả ký tự không phải số
    const numericValue = value.replace(/\D/g, '');
    // Định dạng số với dấu chấm phân cách hàng nghìn
    return numericValue.replace(/\B(?=(\d{3})+(?!\d))/g, '.');
  };

  // Xử lý lưu số dư đầu ngày - dùng ID
  const handleSaveOpeningBalance = async () => {
    if (editingItemId === null) return;

    try {
      const item = filteredData.find(i => i.id === editingItemId);
       if (!item || !item.date) return;

      const newOpeningBalance = parseInt(editableOpeningBalance.replace(/\./g, ''));

      const dateParts = item.date.split('/');
      const reportDateStr = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

      const reportData: any = {
        report_date: reportDateStr,
        opening_balance: newOpeningBalance,
        user_id: 1
      };

      const currentOpeningBalance = parseCurrency(item.openingBalance);
      if (currentOpeningBalance > 0 && currentOpeningBalance !== newOpeningBalance) {
        setPendingReportData(reportData);
        setShowConfirmDialog(true);
        return;
      }

      await saveOpeningBalance(reportData, editingItemId);
    } catch (error) {
      console.error("Lỗi khi cập nhật báo cáo:", error);
      toast.error("Không thể cập nhật báo cáo");
    }
  };

  // Hàm lưu số dư đầu kỳ sau khi đã xác nhận - dùng ID
  const saveOpeningBalance = async (reportData: any, reportId: number) => {
    if (editingItemId === null) return;

     const item = filteredData.find(i => i.id === editingItemId);
     if (!item || !item.date) return;

      const dateParts = item.date.split('/');
      const reportDateStr = `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`;

    try {
      await updateFinancialReport(reportId, reportData);
      console.log("Cập nhật số dư đầu kỳ cho báo cáo có ID:", reportId);

      const updatedReport = await getFinancialReportByDate(reportDateStr);

      if (updatedReport.success && updatedReport.data) {
        const displayData = mapReportToDisplayData(updatedReport.data);
        const updatedData = filteredData.map(dataItem => {
          if (dataItem.id === reportId) {
            return displayData;
          }
          return dataItem;
        });
        setFilteredData(updatedData);
        toast.success("Đã cập nhật số dư đầu ngày và tính toán lại báo cáo");
      } else {
        toast.error("Không thể lấy dữ liệu báo cáo sau khi cập nhật");
      }

      setEditingItemId(null);
      setEditableOpeningBalance("");
      setShowConfirmDialog(false);
      setPendingReportData(null);
    } catch (error) {
      console.error("Lỗi khi cập nhật báo cáo:", error);
      toast.error("Không thể cập nhật báo cáo");
    }
  };

  // Xử lý xác nhận thay đổi số dư đầu kỳ
  const handleConfirmOpeningBalance = () => {
    if (pendingReportData && editingItemId !== null) {
      saveOpeningBalance(pendingReportData, editingItemId);
    }
  };

  // Xử lý hủy thay đổi số dư đầu kỳ
  const handleCancelOpeningBalance = () => {
    setShowConfirmDialog(false);
    setPendingReportData(null);
    setEditingItemId(null);
    setEditableOpeningBalance("");
  };

  // Đã loại bỏ hàm xử lý xuất báo cáo

  // Xử lý chuyển đến ngày/tuần/tháng trước/sau
  const handleNavigateDate = (direction: "prev" | "next") => {
    if (reportType !== "monthly" || !startDate) return;

    let newStartDate = new Date(startDate);
    if (direction === "prev") {
      newStartDate = subMonths(newStartDate, 1);
    } else {
      newStartDate = addMonths(newStartDate, 1);
    }
    setStartDate(startOfMonth(newStartDate));
    setEndDate(endOfMonth(newStartDate));
  };

  // Mobile Card View Component
  const MobileCardView = ({ item }: { item: FinancialDataItem }) => {
    const profit = parseFloat(item.profit);
    const unswipedAmount = parseFloat(item.unswipedAmount);

    const getProfitColor = (profit: number) => {
      if (profit > 0) return "text-emerald-600";
      if (profit < 0) return "text-rose-500";
      return "text-muted-foreground";
    };

    const getUnswipedColor = (amount: number) => {
      if (amount > 0) return "text-amber-600";
      if (amount < 0) return "text-emerald-600";
      return "text-muted-foreground";
    };

    return (
      <Card className="mb-2 shadow-sm">
        <CardContent className="p-3">
          {/* Header: Date + Edit Actions */}
          <div className="flex justify-between items-center mb-2">
            <div className="flex items-center gap-2">
              <span className="font-semibold text-sm">{item.date}</span>
              {item.notes && (
                <FileText className="h-3 w-3 text-muted-foreground" />
              )}
            </div>
            <div className="flex gap-1">
              {item.id && (
                <>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => handleEditOpeningBalance(item.id)}
                    className="h-7 w-7"
                    aria-label="Sửa số dư đầu"
                  >
                    <Edit className="h-3.5 w-3.5" />
                  </Button>
                  <Button
                    size="icon"
                    variant="ghost"
                    onClick={() => handleEditNote(item.id)}
                    className="h-7 w-7"
                    aria-label="Sửa ghi chú"
                  >
                    <FileText className="h-3.5 w-3.5" />
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* Financial Info Grid */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Đầu kỳ:</span>
                <span className="font-medium">{formatCurrency(parseFloat(item.openingBalance))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Thu:</span>
                <span className="font-medium text-emerald-600">{formatCurrency(parseFloat(item.totalIncome))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Chuyển:</span>
                <span className="font-medium text-blue-600">{formatCurrency(parseFloat(item.totalTransfer))}</span>
              </div>
            </div>
            <div className="space-y-1">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Cuối kỳ:</span>
                <span className="font-medium">{formatCurrency(parseFloat(item.closingBalance))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Quẹt:</span>
                <span className="font-medium text-purple-600">{formatCurrency(parseFloat(item.totalSwiped))}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Chưa xử lý:</span>
                <span className={`font-medium ${getUnswipedColor(unswipedAmount)}`}>
                  {formatCurrency(unswipedAmount)}
                </span>
              </div>
            </div>
          </div>

          {/* Profit Row */}
          <div className="flex justify-between items-center mt-2 pt-2 border-t">
            <div className="flex items-center gap-1">
              {profit >= 0 ? (
                <TrendingUp className="h-3 w-3 text-emerald-600" />
              ) : (
                <TrendingDown className="h-3 w-3 text-rose-500" />
              )}
              <span className="text-xs text-muted-foreground">Lợi nhuận:</span>
            </div>
            <span className={`font-semibold text-sm ${getProfitColor(profit)}`}>
              {formatCurrency(profit)}
            </span>
          </div>

          {/* Note Row */}
          {item.notes && (
            <div className="mt-2 pt-2 border-t">
              <p className="text-xs text-muted-foreground truncate">
                <span className="font-medium">Ghi chú:</span> {item.notes}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <CardTitle className="text-lg font-medium">Báo cáo tài chính</CardTitle>
              <CardDescription>
                Theo dõi dòng tiền và hiệu quả tài chính
              </CardDescription>
              {periodLabel && (
                <div className="mt-2 text-sm font-medium text-primary">{periodLabel}</div>
              )}
            </div>

            <div className="flex flex-col sm:flex-row items-center gap-2">
              {/* Report Type Select */}
              <Select value={reportType} onValueChange={handleReportTypeChange}>
                <SelectTrigger className="w-full sm:w-[140px]">
                  <SelectValue placeholder="Loại báo cáo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Ngày</SelectItem>
                  <SelectItem value="monthly">Tháng</SelectItem>
                </SelectContent>
              </Select>

              {/* Date Controls - All in one row */}
              <div className="flex items-center gap-1">
                {/* Previous button for monthly */}
                {reportType === 'monthly' && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleNavigateDate("prev")}
                    disabled={!startDate}
                    className="h-9 w-9"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                )}

                {/* Date Pickers */}
                {reportType === 'daily' ? (
                  <>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "h-9 px-3 justify-start text-left font-normal",
                            !startDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-1 h-3 w-3" />
                          <span className="text-xs">
                            {startDate ? format(startDate, "dd/MM") : "Từ"}
                          </span>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={handleStartDateChange}
                          initialFocus
                          locale={vi}
                        />
                      </PopoverContent>
                    </Popover>

                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "h-9 px-3 justify-start text-left font-normal",
                            !endDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-1 h-3 w-3" />
                          <span className="text-xs">
                            {endDate ? format(endDate, "dd/MM") : "Đến"}
                          </span>
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={handleEndDateChange}
                          initialFocus
                          locale={vi}
                          disabled={(date) => startDate && date < startDate}
                        />
                      </PopoverContent>
                    </Popover>
                  </>
                ) : (
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "h-9 px-3 justify-start text-left font-normal",
                          !startDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-1 h-3 w-3" />
                        <span className="text-xs">
                          {startDate ? format(startDate, "MM/yyyy") : "Tháng"}
                        </span>
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={startDate}
                        onSelect={handleMonthChange}
                        initialFocus
                        locale={vi}
                        defaultMonth={startDate}
                        fromMonth={new Date(2020, 0, 1)}
                        toMonth={new Date()}
                        fixedWeeks
                        showOutsideDays={false}
                      />
                    </PopoverContent>
                  </Popover>
                )}

                {/* Next button for monthly */}
                {reportType === 'monthly' && (
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => handleNavigateDate("next")}
                    disabled={!startDate}
                    className="h-9 w-9"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <Card>
              <CardContent className="pt-6">
                <div className="text-sm text-muted-foreground">Tổng quẹt</div>
                <div className="text-xl sm:text-2xl font-bold text-amber-600 break-words">
                  {formatCurrency(totals.totalSwiped)}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="text-sm text-muted-foreground">Lợi nhuận</div>
                <div className="text-xl sm:text-2xl font-bold text-primary break-words">
                  {formatCurrency(totals.profit)}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Loading State */}
          {isLoading && (
            <div className="flex justify-center items-center py-12">
              <BarChart className="h-6 w-6 animate-pulse mr-2" />
              <span>Đang tải dữ liệu...</span>
            </div>
          )}

          {/* Desktop Table View - Hidden on mobile */}
          {!isLoading && (
            <div className="hidden lg:block">
              <div className="rounded-lg border overflow-hidden">
                <div className="overflow-x-auto">
              <Table className="table-fixed">
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[100px]">{reportType === "daily" ? "Ngày" : "Tháng"}</TableHead>
                    <TableHead className="w-[150px] text-right">
                      {reportType === "daily" ? "Tiền đầu kỳ" : "Số dư trung bình"}
                    </TableHead>
                    <TableHead className="w-[150px] text-right">Tổng thu</TableHead>
                    <TableHead className="w-[150px] text-right">Tổng chuyển</TableHead>
                    {reportType === "daily" && <TableHead className="w-[150px] text-right">Tồn cuối</TableHead>}
                    <TableHead className="w-[150px] text-right">Tiền về</TableHead>
                    <TableHead className="w-[150px] text-right">Tổng quẹt</TableHead>
                    {reportType === "daily" && <TableHead className="w-[150px] text-right">Chưa quẹt</TableHead>}
                    <TableHead className="w-[150px] text-right">Lợi nhuận</TableHead>
                    {reportType === "daily" && <TableHead className="w-[200px]">Ghi chú</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoading ? (
                    <TableRow>
                      <TableCell colSpan={reportType === "daily" ? 10 : 7} className="text-center py-6">
                        Đang tải dữ liệu...
                      </TableCell>
                    </TableRow>
                  ) : filteredData.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={reportType === "daily" ? 10 : 7} className="text-center py-6">
                        Không có dữ liệu báo cáo cho {periodLabel}
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredData.map((item, index) => (
                      <TableRow key={item.id ?? `item-${index}`}>
                        <TableCell className="font-medium">{item.date}</TableCell>
                        <TableCell>
                          {reportType === 'daily' && item.id !== undefined ? (
                            editingItemId === item.id ? (
                              <div className="flex gap-1 items-center justify-end w-full">
                                <Input
                                  className="h-8 flex-grow text-right"
                                  value={editableOpeningBalance}
                                  onChange={(e) => {
                                    const formattedValue = formatNumberInput(e.target.value);
                                    setEditableOpeningBalance(formattedValue);
                                  }}
                                />
                                <Button variant="ghost" size="icon" onClick={handleSaveOpeningBalance} className="h-8 w-8 flex-shrink-0">
                                  <Check className="h-4 w-4 text-green-500" />
                                </Button>
                                <Button variant="ghost" size="icon" onClick={() => setEditingItemId(null)} className="h-8 w-8 flex-shrink-0">
                                  <X className="h-4 w-4 text-red-500" />
                                </Button>
                              </div>
                            ) : (
                              <div className="flex items-center justify-end gap-1 cursor-pointer hover:underline" onClick={() => handleEditOpeningBalance(item.id)}>
                                <span>{formatCurrency(parseFloat(item.openingBalance))}</span>
                                <Edit className="h-3.5 w-3.5 text-muted-foreground flex-shrink-0" />
                              </div>
                            )
                          ) : (
                            <span className="flex justify-end">{formatCurrency(parseFloat(item.openingBalance))}</span>
                          )}
                        </TableCell>
                        <TableCell className="text-right">{formatCurrency(parseFloat(item.totalIncome))}</TableCell>
                        <TableCell className="text-right">{formatCurrency(parseFloat(item.totalTransfer))}</TableCell>
                        {reportType === "daily" ? (
                          <>
                            <TableCell className="text-right">{formatCurrency(parseFloat(item.closingBalance))}</TableCell>
                            <TableCell className="text-right">{formatCurrency(parseFloat(item.receivedAmount))}</TableCell>
                            <TableCell className="text-right">{formatCurrency(parseFloat(item.totalSwiped))}</TableCell>
                            <TableCell className="text-right">{formatCurrency(parseFloat(item.unswipedAmount))}</TableCell>
                            <TableCell className="text-right text-emerald-600 font-medium">{formatCurrency(parseFloat(item.profit))}</TableCell>
                          </>
                        ) : (
                          <>
                            <TableCell className="text-right">{formatCurrency(parseFloat(item.receivedAmount))}</TableCell>
                            <TableCell className="text-right">{formatCurrency(parseFloat(item.totalSwiped))}</TableCell>
                            <TableCell className="text-right text-emerald-600 font-medium">{formatCurrency(parseFloat(item.profit))}</TableCell>
                          </>
                        )}
                        {reportType === "daily" && (
                          <TableCell className="w-[200px]">
                            {item.id !== undefined ? (
                              editingNoteItemId === item.id ? (
                                <div className="flex flex-col gap-1 w-full">
                                  <Textarea
                                    value={editableNote}
                                    onChange={(e) => setEditableNote(e.target.value)}
                                    className="min-h-[60px] w-full text-xs"
                                    placeholder="Ghi chú..."
                                    rows={2}
                                  />
                                  <div className="flex justify-end gap-1">
                                    <Button size="icon" variant="ghost" className="h-6 w-6 flex-shrink-0" onClick={() => setEditingNoteItemId(null)}>
                                      <X className="h-3.5 w-3.5" />
                                    </Button>
                                    <Button size="icon" className="h-6 w-6 flex-shrink-0" onClick={handleSaveNote}>
                                      <Check className="h-3.5 w-3.5" />
                                    </Button>
                                  </div>
                                </div>
                              ) : (
                                <div className="flex items-start justify-between group w-full">
                                  <span className="whitespace-pre-wrap break-words flex-grow py-1">
                                    {item.notes || (reportType === 'daily' && item.id !== undefined ? <span className="text-muted-foreground italic">Thêm ghi chú...</span> : '')}
                                  </span>
                                  {reportType === 'daily' && item.id !== undefined && (
                                    <Button variant="ghost" size="icon" onClick={() => handleEditNote(item.id)} className="ml-2 h-6 w-6 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0">
                                      <Edit className="h-3 w-3" />
                                    </Button>
                                  )}
                                </div>
                              )
                            ) : (
                              <span className="whitespace-pre-wrap break-words py-1">{item.notes || ""}</span>
                            )}
                          </TableCell>
                        )}
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
                </div>
              </div>
            </div>
          )}

          {/* Mobile Card View - Visible on mobile and tablet */}
          {!isLoading && (
            <div className="lg:hidden">
              {filteredData.length > 0 ? (
                <div className="space-y-4">
                  {filteredData.map((item, index) => (
                    <MobileCardView key={item.id ?? `item-${index}`} item={item} />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <BarChart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">
                    Không có dữ liệu báo cáo cho {periodLabel}
                  </p>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Dialog xác nhận thay đổi số dư đầu kỳ */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-md" aria-describedby="export-report-description">
          <DialogHeader>
            <DialogTitle>Xác nhận thay đổi số dư đầu kỳ</DialogTitle>
            <DialogDescription id="export-report-description">
              Bạn đang thay đổi số dư đầu kỳ cho ngày có dữ liệu tồn tại.
              Việc thay đổi này sẽ ảnh hưởng đến tính toán số dư cuối kỳ.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm text-muted-foreground mb-2">Số dư đầu kỳ mới:</p>
            <p className="text-lg font-medium">{formatCurrency(parseCurrency(pendingReportData?.opening_balance || '0'))}</p>
          </div>
          <DialogFooter className="flex flex-row justify-end gap-2">
            <Button variant="outline" onClick={handleCancelOpeningBalance}>
              Hủy bỏ
            </Button>
            <Button onClick={handleConfirmOpeningBalance}>
              Xác nhận
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
